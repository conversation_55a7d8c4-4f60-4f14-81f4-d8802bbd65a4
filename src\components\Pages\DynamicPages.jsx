import React, { useState, useRef } from "react";
import {
  Card,
  Tabs,
  Button,
  Form,
  Input,
  Select,
  Space,
  Row,
  Col,
  Typography,
  message,
  Tooltip,
  Divider,
} from "antd";
import {
  Download,
  Upload,
  Plus,
  Minus,
  Info,
  Settings,
  Eye,
} from "lucide-react";

const { Title, Text } = Typography;
const { Option } = Select;

const DynamicPages = () => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState("city");
  const [sections, setSections] = useState([
    {
      id: 1,
      label: "",
      items: [
        { id: 1, label: "", slug: "" },
        { id: 2, label: "", slug: "" },
      ],
    },
  ]);
  const [importing, setImporting] = useState(false);
  const [exporting, setExporting] = useState(false);
  const fileInputRef = useRef(null);

  // Tab configuration
  const tabItems = [
    { key: "city", label: "City" },
    { key: "service", label: "Service" },
    { key: "page2", label: "Page 2" },
    { key: "page3", label: "Page 3" },
  ];

  // Navigation type options
  const navigationTypes = [
    { value: "single", label: "Single Column" },
    { value: "multi", label: "Multi Columns" },
    { value: "dropdown", label: "Dropdown" },
    { value: "mega", label: "Mega Menu" },
  ];

  // Add new field to a section
  const addField = (sectionId) => {
    setSections((prev) =>
      prev.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              items: [
                ...section.items,
                { id: Date.now(), label: "", slug: "" },
              ],
            }
          : section
      )
    );
  };

  // Remove field from a section
  const removeField = (sectionId, fieldId) => {
    setSections((prev) =>
      prev.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              items: section.items.filter((item) => item.id !== fieldId),
            }
          : section
      )
    );
  };

  // Add new section
  const addSection = () => {
    const newSection = {
      id: Date.now(),
      label: "",
      items: [{ id: Date.now() + 1, label: "", slug: "" }],
    };
    setSections((prev) => [...prev, newSection]);
  };

  // Remove section
  const removeSection = (sectionId) => {
    if (sections.length > 1) {
      setSections((prev) => prev.filter((section) => section.id !== sectionId));
    }
  };

  // Update section label
  const updateSectionLabel = (sectionId, label) => {
    setSections((prev) =>
      prev.map((section) =>
        section.id === sectionId ? { ...section, label } : section
      )
    );
  };

  // Update field values
  const updateField = (sectionId, fieldId, field, value) => {
    setSections((prev) =>
      prev.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              items: section.items.map((item) =>
                item.id === fieldId ? { ...item, [field]: value } : item
              ),
            }
          : section
      )
    );
  };

  // Import from JSON
  const handleImportJSON = () => {
    fileInputRef.current?.click();
  };

  const onFileChange = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setImporting(true);
    try {
      const text = await file.text();
      const parsed = JSON.parse(text);

      if (parsed.sections) {
        setSections(parsed.sections);
      }
      if (parsed.formData) {
        form.setFieldsValue(parsed.formData);
      }

      message.success("JSON imported successfully");
    } catch (err) {
      console.error("Import JSON error:", err);
      message.error("Failed to import JSON. Please check the file format.");
    } finally {
      setImporting(false);
      if (fileInputRef.current) fileInputRef.current.value = "";
    }
  };

  // Export to JSON
  const handleExportJSON = async () => {
    setExporting(true);
    try {
      const formData = form.getFieldsValue();
      const exportData = {
        formData,
        sections,
        exportDate: new Date().toISOString(),
        version: "1.0.0",
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `dynamic-pages-${activeTab}-${Date.now()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success("Data exported successfully");
    } catch (err) {
      console.error("Export error:", err);
      message.error("Failed to export data");
    } finally {
      setExporting(false);
    }
  };

  return (
    <div className="tw-min-h-screen tw-bg-gray-50 tw-p-6">
      <div className="tw-max-w-7xl tw-mx-auto">
        {/* Header */}
        <div className="tw-mb-6">
          <Title level={2} className="!tw-mb-2 !tw-text-gray-900">
            Dream Builder v.0.0
          </Title>
          <Text type="secondary" className="tw-text-base">
            Configure your website settings and content
          </Text>
        </div>

        {/* Main Content Card */}
        <Card className="tw-shadow-sm tw-border-0 tw-rounded-2xl">
          {/* Tab Navigation */}
          <div className="tw-mb-6">
            <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
              <div className="tw-flex tw-space-x-4">
                <Button
                  type="primary"
                  icon={<Settings className="tw-w-4 tw-h-4" />}
                  className="tw-bg-blue-600 hover:tw-bg-blue-700 tw-border-0 tw-rounded-lg tw-px-6"
                >
                  Dynamic Pages
                </Button>
                <Button
                  icon={<Eye className="tw-w-4 tw-h-4" />}
                  className="tw-border-gray-300 tw-rounded-lg tw-px-6"
                >
                  Branding & Content Preview
                </Button>
              </div>

              <div className="tw-flex tw-space-x-3">
                <Button
                  type="primary"
                  icon={<Upload className="tw-w-4 tw-h-4" />}
                  onClick={handleImportJSON}
                  loading={importing}
                  className="tw-bg-blue-600 hover:tw-bg-blue-700 tw-border-0 tw-rounded-lg"
                >
                  Import From JSON
                </Button>
                <Button
                  icon={<Download className="tw-w-4 tw-h-4" />}
                  onClick={handleExportJSON}
                  loading={exporting}
                  className="tw-border-gray-300 tw-rounded-lg"
                >
                  Export
                </Button>
              </div>
            </div>

            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={tabItems}
              className="dynamic-pages-tabs"
            />
          </div>

          <input
            type="file"
            ref={fileInputRef}
            onChange={onFileChange}
            accept=".json"
            style={{ display: "none" }}
          />

          {/* Form Content */}
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              sitemapLabel: "",
              navigationType: "multi",
              columnCount: "2",
            }}
          >
            <Title level={4} className="!tw-mb-4 !tw-text-gray-900">
              Dynamic Page Details
            </Title>

            {/* Basic Configuration */}
            <Row gutter={[24, 16]}>
              <Col xs={24} sm={12} lg={8}>
                <Form.Item
                  name="sitemapLabel"
                  label={
                    <Space className="tw-items-center">
                      <Text strong>Sitemap Label</Text>
                      <Tooltip title="Enter the label for sitemap navigation">
                        <Info className="tw-w-3 tw-h-3 tw-text-gray-400" />
                      </Tooltip>
                    </Space>
                  }
                >
                  <Input
                    placeholder="Enter sitemap label"
                    className="tw-rounded-lg"
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Form.Item
                  name="navigationType"
                  label={
                    <Space className="tw-items-center">
                      <Text strong>Navigation Type</Text>
                      <Tooltip title="Select the navigation display type">
                        <Info className="tw-w-3 tw-h-3 tw-text-gray-400" />
                      </Tooltip>
                    </Space>
                  }
                >
                  <Select
                    placeholder="Select navigation type"
                    className="tw-rounded-lg"
                  >
                    {navigationTypes.map((type) => (
                      <Option key={type.value} value={type.value}>
                        {type.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Form.Item
                  name="columnCount"
                  label={
                    <Space className="tw-items-center">
                      <Text strong>Column Count</Text>
                      <Tooltip title="Number of columns for multi-column layout">
                        <Info className="tw-w-3 tw-h-3 tw-text-gray-400" />
                      </Tooltip>
                    </Space>
                  }
                >
                  <Select
                    placeholder="Select column count"
                    className="tw-rounded-lg"
                  >
                    <Option value="1">1 Column</Option>
                    <Option value="2">2 Columns</Option>
                    <Option value="3">3 Columns</Option>
                    <Option value="4">4 Columns</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Divider className="tw-my-6" />

            {/* Section Management */}
            <div className="tw-mb-6">
              <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
                <Title level={5} className="!tw-mb-0 !tw-text-gray-900">
                  Sections
                </Title>
                <Button
                  type="dashed"
                  icon={<Plus className="tw-w-4 tw-h-4" />}
                  onClick={addSection}
                  className="tw-border-blue-300 tw-text-blue-600 hover:tw-border-blue-500 hover:tw-text-blue-700 tw-rounded-lg"
                >
                  Add Section
                </Button>
              </div>

              {sections.map((section, sectionIndex) => (
                <Card
                  key={section.id}
                  className="tw-mb-4 tw-border tw-border-gray-200 tw-rounded-lg"
                  size="small"
                >
                  <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
                    <Input
                      placeholder="Enter section label"
                      value={section.label}
                      onChange={(e) =>
                        updateSectionLabel(section.id, e.target.value)
                      }
                      className="tw-flex-1 tw-mr-4 tw-rounded-lg"
                    />
                    {sections.length > 1 && (
                      <Button
                        type="text"
                        danger
                        icon={<Minus className="tw-w-4 tw-h-4" />}
                        onClick={() => removeSection(section.id)}
                        className="tw-rounded-lg"
                      />
                    )}
                  </div>

                  <div className="tw-space-y-3">
                    <Text strong className="tw-text-gray-700">
                      Section Items
                    </Text>

                    {section.items.map((item, itemIndex) => (
                      <Row key={item.id} gutter={[12, 0]} align="middle">
                        <Col xs={24} sm={10}>
                          <Input
                            placeholder="Enter label"
                            value={item.label}
                            onChange={(e) =>
                              updateField(
                                section.id,
                                item.id,
                                "label",
                                e.target.value
                              )
                            }
                            className="tw-rounded-lg"
                          />
                        </Col>
                        <Col xs={24} sm={10}>
                          <Input
                            placeholder="Enter slug"
                            value={item.slug}
                            onChange={(e) =>
                              updateField(
                                section.id,
                                item.id,
                                "slug",
                                e.target.value
                              )
                            }
                            className="tw-rounded-lg"
                          />
                        </Col>
                        <Col xs={24} sm={4} className="tw-text-right">
                          <Button
                            type="text"
                            danger
                            icon={<Minus className="tw-w-4 tw-h-4" />}
                            onClick={() => removeField(section.id, item.id)}
                            disabled={section.items.length <= 1}
                            className="tw-rounded-lg"
                          />
                        </Col>
                      </Row>
                    ))}

                    <Button
                      type="dashed"
                      icon={<Plus className="tw-w-4 tw-h-4" />}
                      onClick={() => addField(section.id)}
                      className="tw-w-full tw-border-gray-300 tw-text-gray-600 hover:tw-border-gray-400 hover:tw-text-gray-700 tw-rounded-lg"
                    >
                      Add Field
                    </Button>
                  </div>
                </Card>
              ))}
            </div>

            {/* Export Button */}
            <div className="tw-flex tw-justify-end tw-pt-4 tw-border-t tw-border-gray-200">
              <Button
                type="primary"
                icon={<Download className="tw-w-4 tw-h-4" />}
                onClick={handleExportJSON}
                loading={exporting}
                size="large"
                className="tw-bg-blue-600 hover:tw-bg-blue-700 tw-border-0 tw-rounded-lg tw-px-8"
              >
                Export
              </Button>
            </div>
          </Form>
        </Card>
      </div>
    </div>
  );
};

export default DynamicPages;
