import React, { useState, useCallback, useEffect } from "react";
import {
  Button,
  Upload,
  Card,
  Row,
  Col,
  Popconfirm,
  Typography,
  Empty,
  Spin,
  Tag,
  Dropdown,
  message,
} from "antd";
import { CheckCircle } from "lucide-react";
import {
  UploadOutlined,
  DeleteOutlined,
  LinkOutlined,
} from "@ant-design/icons";
import useMediaManager from "../../../hooks/useMediaManager";
import SearchBar from "../../common/SearchBar";

const { Text, Title } = Typography;

const MediaBar = ({ fileList, setFileList, setFormData, formData }) => {
  const {
    mediaFiles,
    uploading,
    uploadFile,
    deleteFile,
    searchFiles,
    getFileUrl,
  } = useMediaManager();

  const [searchTerm, setSearchTerm] = useState("");

  // Update filtered files when mediaFiles or searchTerm changes
  useEffect(() => {
    const filteredFiles = searchFiles(searchTerm);
    // Convert to object structure with image keys
    const fileListObject = {};
    filteredFiles.forEach((file) => {
      const imageKey = getImageKey(file.originalName);
      fileListObject[imageKey] = file;
    });
    setFileList(fileListObject);
  }, [mediaFiles, searchTerm, searchFiles, setFileList]);

  // Handle search input
  const handleSearch = useCallback((value) => {
    setSearchTerm(value);
  }, []);

  // Extract image name without extension for content key matching
  const getImageKey = (filename) => {
    return filename.replace(/\.[^/.]+$/, ""); // Remove file extension
  };

  // Auto-replace image placeholders in content when image is uploaded
  const updateContentWithImage = useCallback(
    (uploadedFile) => {
      const imageKey = getImageKey(uploadedFile.originalName);
      // Use blob URL for immediate display, fallback to path
      const imageUrl = uploadedFile.blobUrl || uploadedFile.path;

      // Update contentJSON with image URL (blob for immediate display)
      if (formData?.contentJSON) {
        const updatedContentJSON = { ...formData.contentJSON };

        // Search through all content levels to find matching keys
        const updateImageInContent = (obj, key, url) => {
          if (typeof obj === "object" && obj !== null) {
            Object.keys(obj).forEach((objKey) => {
              if (objKey === key || objKey.includes(key)) {
                // Update image src with blob URL for immediate display
                obj[objKey] = url;
              } else if (typeof obj[objKey] === "object") {
                updateImageInContent(obj[objKey], key, url);
              }
            });
          }
        };

        updateImageInContent(updatedContentJSON, imageKey, imageUrl);

        // Update formData with new contentJSON
        setFormData((prev) => ({
          ...prev,
          contentJSON: updatedContentJSON,
        }));
      }
    },
    [formData?.contentJSON, setFormData]
  );

  // Handle file upload with content replacement
  const handleUpload = async (file) => {
    try {
      const uploadedFile = await uploadFile(file);
      if (uploadedFile) {
        // Auto-update content with uploaded image
        updateContentWithImage(uploadedFile);
      }
    } catch (error) {
      // Error handling is done in the hook
    }
    return false; // Prevent default upload behavior
  };

  // Handle file deletion
  const handleDelete = async (fileId) => {
    try {
      await deleteFile(fileId);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  // Get all available content keys that could be image placeholders
  const getAvailableContentKeys = useCallback(() => {
    const keys = [];

    const extractKeys = (obj, prefix = "") => {
      if (typeof obj === "object" && obj !== null) {
        Object.keys(obj).forEach((key) => {
          const fullKey = prefix ? `${prefix}.${key}` : key;

          // Check if key might be for images (contains image-related keywords)
          if (
            key.toLowerCase().includes("image") ||
            key.toLowerCase().includes("img") ||
            key.toLowerCase().includes("photo") ||
            key.toLowerCase().includes("picture") ||
            key.toLowerCase().includes("hero") ||
            key.toLowerCase().includes("banner") ||
            key.toLowerCase().includes("logo")
          ) {
            keys.push({ key: fullKey, value: obj[key] });
          }

          if (typeof obj[key] === "object" && obj[key] !== null) {
            extractKeys(obj[key], fullKey);
          }
        });
      }
    };

    if (formData?.contentJSON) {
      extractKeys(formData.contentJSON);
    }

    return keys;
  }, [formData?.contentJSON]);

  // Handle manual image assignment to content key
  const handleAssignToContent = useCallback(
    (file, contentKey) => {
      if (formData?.contentJSON) {
        const updatedContentJSON = { ...formData.contentJSON };

        // Find and update the specific content key
        const updateSpecificKey = (obj, targetKey, imagePath) => {
          if (typeof obj === "object" && obj !== null) {
            Object.keys(obj).forEach((objKey) => {
              if (objKey === targetKey) {
                obj[objKey] = imagePath;
              } else if (typeof obj[objKey] === "object") {
                updateSpecificKey(obj[objKey], targetKey, imagePath);
              }
            });
          }
        };

        updateSpecificKey(updatedContentJSON, contentKey, file.path);

        setFormData((prev) => ({
          ...prev,
          contentJSON: updatedContentJSON,
        }));

        message.success(
          `Image "${file.originalName}" assigned to "${contentKey}"`
        );
      }
    },
    [formData?.contentJSON, setFormData]
  );

  // Check if image is already assigned to content
  const isImageAssigned = useCallback(
    (file) => {
      const availableKeys = getAvailableContentKeys();
      return availableKeys.some((item) => item.value === file.path);
    },
    [getAvailableContentKeys]
  );

  // Get the content key that this image is assigned to
  const getAssignedContentKey = useCallback(
    (file) => {
      const availableKeys = getAvailableContentKeys();
      const assigned = availableKeys.find((item) => item.value === file.path);
      return assigned ? assigned.key : null;
    },
    [getAvailableContentKeys]
  );

  return (
    <div className="tw-h-full tw-flex tw-flex-col tw-overflow-hidden">
      {/* Header Section - Fixed */}
      <div className="tw-flex-shrink-0 tw-p-4 tw-border-b tw-border-gray-200">
        <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
          <Title level={4} className="!tw-m-0 tw-flex tw-items-center">
            Media
          </Title>

          <Upload
            beforeUpload={handleUpload}
            showUploadList={false}
            accept="image/*"
            disabled={uploading}
          >
            <Button
              type="primary"
              size="large"
              title=""
              loading={uploading}
              className="tw-px-6 tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Upload
            </Button>
          </Upload>
        </div>

        {/* Search Bar */}
        <SearchBar handleSearch={(e) => handleSearch(e)} />
      </div>

      {/* Scrollable Media Files List */}
      <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
        {uploading && (
          <div className="tw-text-center tw-py-4">
            <Spin size="large" />
            <Text className="tw-block tw-mt-2 tw-text-gray-500">
              Uploading...
            </Text>
          </div>
        )}

        {Object.keys(fileList || {}).length === 0 && !uploading ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              searchTerm
                ? `No files found matching "${searchTerm}"`
                : "No media files uploaded yet"
            }
            className="tw-py-8"
          >
            {/* {!searchTerm && (
              <Upload
                beforeUpload={handleUpload}
                showUploadList={false}
                accept="image/*"
                multiple
                disabled={uploading}
              >
                <Button type="primary" icon={<UploadOutlined />}>
                  Upload Your First File
                </Button>
              </Upload>
            )} */}
          </Empty>
        ) : (
          <Row gutter={[16, 16]}>
            {Object.entries(fileList || {}).map(([imageKey, file]) => {
              const assignedKey = getAssignedContentKey(file);
              const isAssigned = isImageAssigned(file);
              const availableKeys = getAvailableContentKeys();
              const displayUrl = getFileUrl(file); // Get blob URL or fallback to path

              // Create dropdown menu items for content key assignment
              const assignmentMenuItems = [
                ...availableKeys.map((item) => ({
                  key: item.key,
                  label: (
                    <div className="tw-flex tw-items-center tw-justify-between">
                      <span>{item.key}</span>
                      {item.value === file.path && (
                        <CheckCircle className="tw-w-4 tw-h-4 tw-text-green-500 tw-ml-2" />
                      )}
                    </div>
                  ),
                  onClick: () => handleAssignToContent(file, item.key),
                  className: item.value === file.path ? "tw-bg-blue-50" : "",
                })),
                ...(availableKeys.length === 0
                  ? [
                      {
                        key: "no-keys",
                        label: "No image content keys found",
                        disabled: true,
                      },
                    ]
                  : []),
              ];

              return (
                <Col xs={24} sm={12} md={12} lg={12} key={imageKey}>
                  <Card
                    className="tw-h-full tw-border tw-border-gray-200 hover:tw-border-blue-300 tw-transition-all tw-duration-200"
                    styles={{ body: { padding: "8px 12px" } }}
                  >
                    <div className="tw-space-y-3">
                      {/* Image Preview */}
                      {displayUrl && (
                        <div className="tw-flex tw-justify-center tw-mb-3">
                          <img
                            src={displayUrl}
                            alt={file.originalName}
                            className="tw-w-full tw-h-24 tw-object-cover tw-rounded-lg tw-border tw-border-gray-200"
                            onError={(e) => {
                              // Fallback if blob URL fails
                              e.target.style.display = "none";
                            }}
                          />
                        </div>
                      )}

                      {/* File Info Header */}
                      <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-flex-1 tw-min-w-0">
                          <Text
                            className="tw-block tw-text-sm tw-font-medium tw-text-gray-900 tw-truncate"
                            title={file.originalName}
                          >
                            {file.originalName}
                          </Text>
                        </div>

                        <Popconfirm
                          title="Delete this file?"
                          description="This action cannot be undone."
                          onConfirm={() => handleDelete(imageKey)}
                          okText="Delete"
                          cancelText="Cancel"
                          okButtonProps={{ danger: true }}
                        >
                          <Button
                            type="text"
                            danger
                            size="small"
                            icon={<DeleteOutlined />}
                          />
                        </Popconfirm>
                      </div>

                      {/* Assignment Status */}
                      {/* <div className="tw-space-y-2">
                        {isAssigned ? (
                          <Tag
                            color="green"
                            className="tw-w-full tw-text-center"
                          >
                            <CheckCircle className="tw-w-3 tw-h-3 tw-mr-1" />
                            Assigned to: {assignedKey}
                          </Tag>
                        ) : (
                          <Tag
                            color="orange"
                            className="tw-w-full tw-text-center"
                          >
                            Not assigned
                          </Tag>
                        )}

                        
                        <Dropdown
                          menu={{ items: assignmentMenuItems }}
                          trigger={["click"]}
                          disabled={availableKeys.length === 0}
                        >
                          <Button
                            size="small"
                            icon={<LinkOutlined />}
                            className="tw-w-full"
                            type={isAssigned ? "default" : "primary"}
                          >
                            {isAssigned ? "Reassign" : "Assign to Content"}
                          </Button>
                        </Dropdown>
                      </div> */}
                    </div>
                  </Card>
                </Col>
              );
            })}
          </Row>
        )}
      </div>
    </div>
  );
};

export default MediaBar;
