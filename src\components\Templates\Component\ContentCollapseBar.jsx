import React, { useCallback, useMemo, useRef, useState } from "react";
import SearchBar from "../../common/SearchBar";
import { Button, Collapse, Input, message } from "antd";
import JsonContentCollapse from "../../common/JsonContentCollapse";
import { addAtPath, deleteAtPath, updateAtPath } from "../../../util/functions";
// import { buildItemList } from "../../../util/functions";

const { Panel } = Collapse;
const { TextArea } = Input;

const ContentCollapseBar = ({ contentJSON, setContentJSON, saving }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedAll, setExpandedAll] = useState(false);
  const [importing, setImporting] = useState(false);
  const fileInputRef = useRef(null);

  const handleAddItem = useCallback((path) => {
    try {
      setContentJSON((prev) => addAtPath(prev, path));
      message.success("Item added");
    } catch (e) {
      console.error("Error in handleAddItem:", e);
      message.error("Failed to add item");
    }
  }, []);

  const handleDelete = useCallback((path, fieldKey = null) => {
    try {
      setContentJSON((prev) => deleteAtPath(prev, path, fieldKey));
      message.success("Item deleted successfully");
    } catch (e) {
      console.error("Error in handleDelete:", e);
      message.error("Failed to delete item");
    }
  }, []);

  const buildItemList = (data, expandedAll, path = []) => {
    if (Array.isArray(data)) {
      return data.map((item, index) => ({
        key: [...path, index].join("_"),
        label: `Position ${index + 1}`,
        children: (
          <>
            {/* recurse deeper only if object/array */}
            {typeof item === "object" && item !== null ? (
              <JsonContentCollapse
                expanded={expandedAll}
                itemList={buildItemList(item, expandedAll, [...path, index])}
                onDeleteItem={(it) => handleDelete([...path, index], it.key)}
              />
            ) : (
              <TextArea
                rows={2}
                value={item}
                onChange={(e) =>
                  setContentJSON((prev) =>
                    updateAtPath(prev, [...path, index], () => e.target.value)
                  )
                }
              />
            )}

            {/* <div className="tw-mt-2">
              <Button type="dashed" onClick={() => handleAddItem(path)}>
                + Add Item
              </Button>
              <Button
                danger
                size="small"
                style={{ marginLeft: "6px" }}
                onClick={() => handleDelete(path, null)}
              >
                Delete This
              </Button>
            </div> */}
          </>
        ),
      }));
    } else if (typeof data === "object" && data !== null) {
      return Object.keys(data).map((key) => ({
        key: [...path, key].join("_"),
        label: key,
        isDelete: true,
        onDelete: () => handleDelete(path, key),
        children:
          typeof data[key] === "object" && data[key] !== null ? (
            <>
              <JsonContentCollapse
                expanded={expandedAll}
                itemList={buildItemList(data[key], expandedAll, [...path, key])}
                onDeleteItem={(it) => handleDelete([...path, key], it.key)}
              />
              {Array.isArray(data[key]) && data[key].length > 0 && (
                <div className="tw-mt-2 tw-w-full tw-flex tw-justify-center tw-items-center">
                  <Button
                    type="dashed"
                    className="tw-w-full"
                    onClick={() => handleAddItem([...path, key])}
                  >
                    + Add Item
                  </Button>
                </div>
              )}
            </>
          ) : (
            <TextArea
              rows={2}
              value={data[key]}
              onChange={(e) =>
                setContentJSON((prev) =>
                  updateAtPath(prev, [...path, key], () => e.target.value)
                )
              }
            />
          ),
      }));
    } else {
      // ✅ Primitive leaf node → directly render input, no nested collapse
      return [
        {
          key: path.join("_"),
          label: path[path.length - 1],
          children: (
            <TextArea
              rows={2}
              value={data}
              onChange={(e) =>
                setContentJSON((prev) =>
                  updateAtPath(prev, path, () => e.target.value)
                )
              }
            />
          ),
        },
      ];
    }
  };

  const itemList = useMemo(() => {
    if (!contentJSON) return [];
    return Object.keys(contentJSON).map((key) => ({
      key,
      label: key,
      extra: Array.isArray(contentJSON[key]),
      children: (
        <>
          <JsonContentCollapse
            expanded={expandedAll}
            itemList={buildItemList(contentJSON[key], expandedAll, [key])}
            onDeleteItem={(it) => handleDelete([key], it.key)}
          />
          {Array.isArray(contentJSON[key]) && contentJSON[key].length > 0 && (
            <div className="tw-mt-2 tw-w-full tw-flex tw-justify-center tw-items-center">
              <Button
                className="tw-w-full"
                type="dashed"
                onClick={() => handleAddItem([key])}
              >
                + Add Item
              </Button>
            </div>
          )}
        </>
      ),
    }));
  }, [contentJSON, expandedAll, handleDelete, handleAddItem]);

  const toggleExpandAll = () => {
    setExpandedAll((prev) => !prev);
  };

  const handleImportJSON = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };

  const onFileChange = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setImporting(true);
    try {
      const text = await file.text();
      const parsed = JSON.parse(text);
      if (!parsed || typeof parsed !== "object" || Array.isArray(parsed)) {
        throw new Error("Invalid JSON structure. Expected an object at root.");
      }
      setContentJSON(parsed);
      message.success("JSON imported successfully");
    } catch (err) {
      console.error("Import JSON error:", err);
      message.error("Failed to import JSON. Please check the file.");
    } finally {
      setImporting(false);
      // Reset input value to re-trigger change event if same file is selected again
      if (fileInputRef.current) fileInputRef.current.value = "";
    }
  };
  return (
    <div className="tw-h-full tw-flex tw-flex-col tw-overflow-hidden">
      {/* Header Section - Fixed */}
      <div className="tw-flex-shrink-0 tw-p-4 tw-border-b tw-border-gray-200">
        <div className="tw-mb-4">
          <SearchBar handleSearch={(e) => setSearchTerm(e)} />
        </div>
        <div className="tw-flex tw-space-x-4 tw-w-full tw-justify-between tw-items-center">
          <Button
            type="primary"
            size="large"
            onClick={handleImportJSON}
            disabled={saving || importing}
            className="tw-px-6 tw-w-full tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
          >
            Import JSON
          </Button>

          <Button
            size="large"
            onClick={toggleExpandAll}
            className="tw-flex tw-w-full tw-items-center tw-text-black tw-border-blue-200 hover:tw-bg-blue-50"
          >
            {expandedAll ? "Collapse All" : "Expand All"}
          </Button>
        </div>
        {/* Hidden file input for JSON import */}
        <input
          type="file"
          accept="application/json,.json"
          ref={fileInputRef}
          onChange={onFileChange}
          style={{ display: "none" }}
        />
      </div>

      {/* Scrollable Content Section */}
      <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
        {itemList?.length ? (
          <JsonContentCollapse itemList={itemList} expanded={expandedAll} />
        ) : (
          <div className="tw-text-center tw-py-8">
            <p className="tw-text-gray-500 tw-mb-2">No content fields found</p>
            <p className="tw-text-sm tw-text-gray-400">
              Add content fields to see them here
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentCollapseBar;
